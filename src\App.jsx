import { useState, useEffect, useRef } from 'react'
import QRCode from 'qrcode'
import { ChromePicker } from 'react-color'
import { Download, Upload, QrCode, Palette, Type, Image } from 'lucide-react'
import './App.css'

function App() {
  const [currentStep, setCurrentStep] = useState(1)
  const [qrData, setQrData] = useState('')
  const [qrColor, setQrColor] = useState('#000000')
  const [bgColor, setBgColor] = useState('#ffffff')
  const [qrSize, setQrSize] = useState(300)
  const [logo, setLogo] = useState(null)
  const [logoPosition, setLogoPosition] = useState('center')
  const [logoSize, setLogoSize] = useState(20) // Percentage of QR code size
  const [qrCodeUrl, setQrCodeUrl] = useState('')
  const [showColorPicker, setShowColorPicker] = useState(false)
  const [showBgColorPicker, setShowBgColorPicker] = useState(false)
  const canvasRef = useRef(null)
  const fileInputRef = useRef(null)

  // Generate QR code whenever data, colors, size, or logo settings change
  useEffect(() => {
    if (qrData) {
      generateQRCode()
    }
  }, [qrData, qrColor, bgColor, qrSize, logo, logoPosition, logoSize])

  const generateQRCode = async () => {
    try {
      const canvas = canvasRef.current
      if (!canvas) {
        console.error('Canvas not found')
        return
      }

      // Set canvas size
      canvas.width = qrSize
      canvas.height = qrSize

      const ctx = canvas.getContext('2d')

      // Clear canvas first
      ctx.clearRect(0, 0, canvas.width, canvas.height)

      console.log('Generating QR code with data:', qrData)

      // Generate QR code first
      await QRCode.toCanvas(canvas, qrData, {
        width: qrSize,
        margin: 2,
        color: {
          dark: qrColor,
          light: bgColor
        },
        errorCorrectionLevel: 'M'
      })

      console.log('QR code generated successfully')

      // Add logo if present
      if (logo) {
        console.log('Adding logo to QR code...')

        // Create a new image element
        const logoImg = new Image()

        logoImg.onload = () => {
          console.log('Logo image loaded, drawing on canvas...')

          try {
            // Calculate logo size (percentage of QR code size)
            const actualLogoSize = Math.max(30, (qrSize * logoSize) / 100)

            // Calculate position
            let x, y
            const margin = 20

            switch (logoPosition) {
              case 'top-left':
                x = margin
                y = margin
                break
              case 'top-center':
                x = (canvas.width - actualLogoSize) / 2
                y = margin
                break
              case 'top-right':
                x = canvas.width - actualLogoSize - margin
                y = margin
                break
              case 'center-left':
                x = margin
                y = (canvas.height - actualLogoSize) / 2
                break
              case 'center':
                x = (canvas.width - actualLogoSize) / 2
                y = (canvas.height - actualLogoSize) / 2
                break
              case 'center-right':
                x = canvas.width - actualLogoSize - margin
                y = (canvas.height - actualLogoSize) / 2
                break
              case 'bottom-left':
                x = margin
                y = canvas.height - actualLogoSize - margin
                break
              case 'bottom-center':
                x = (canvas.width - actualLogoSize) / 2
                y = canvas.height - actualLogoSize - margin
                break
              case 'bottom-right':
                x = canvas.width - actualLogoSize - margin
                y = canvas.height - actualLogoSize - margin
                break
              default:
                x = (canvas.width - actualLogoSize) / 2
                y = (canvas.height - actualLogoSize) / 2
            }

            console.log('Logo position calculated:', { x, y, size: actualLogoSize, position: logoPosition })

            // Save canvas state
            ctx.save()

            // Create white background for logo
            const bgPadding = 8
            ctx.fillStyle = '#ffffff'
            ctx.fillRect(x - bgPadding, y - bgPadding, actualLogoSize + (bgPadding * 2), actualLogoSize + (bgPadding * 2))

            // Add subtle border
            ctx.strokeStyle = '#e0e0e0'
            ctx.lineWidth = 1
            ctx.strokeRect(x - bgPadding, y - bgPadding, actualLogoSize + (bgPadding * 2), actualLogoSize + (bgPadding * 2))

            // Draw the logo
            ctx.drawImage(logoImg, x, y, actualLogoSize, actualLogoSize)

            // Restore canvas state
            ctx.restore()

            console.log('Logo drawn successfully!')

            // Update download URL
            setQrCodeUrl(canvas.toDataURL('image/png', 1.0))

          } catch (drawError) {
            console.error('Error drawing logo on canvas:', drawError)
          }
        }

        logoImg.onerror = (error) => {
          console.error('Failed to load logo image:', error)
        }

        // Load the logo image
        logoImg.src = logo
      } else {
        // No logo, just update download URL
        setQrCodeUrl(canvas.toDataURL('image/png', 1.0))
      }

    } catch (error) {
      console.error('Error in generateQRCode:', error)
    }
  }

  const handleLogoUpload = (event) => {
    const file = event.target.files[0]
    if (file) {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        alert('Please select a valid image file')
        return
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        alert('Image file size should be less than 5MB')
        return
      }

      const reader = new FileReader()
      reader.onload = (e) => {
        const img = new Image()
        img.onload = () => {
          console.log('Logo image loaded successfully:', {
            width: img.width,
            height: img.height,
            src: e.target.result.substring(0, 50) + '...'
          })
          setLogo(e.target.result)
        }
        img.onerror = () => {
          console.error('Failed to load logo image')
          alert('Failed to load the selected image. Please try another image.')
        }
        img.src = e.target.result
      }
      reader.onerror = () => {
        console.error('Failed to read file')
        alert('Failed to read the selected file. Please try again.')
      }
      reader.readAsDataURL(file)
    }
  }

  const downloadQR = () => {
    const canvas = canvasRef.current
    if (canvas) {
      const link = document.createElement('a')
      link.download = `qrcode-${qrSize}x${qrSize}.png`
      link.href = canvas.toDataURL('image/png', 1.0)
      link.click()
    }
  }

  const steps = [
    { number: 1, title: 'Enter Data', icon: Type },
    { number: 2, title: 'Choose Style', icon: Palette },
    { number: 3, title: 'Upload Logo', icon: Image },
    { number: 4, title: 'Download QR', icon: Download }
  ]

  return (
    <div className="app">
      <header className="header">
        <div className="container">
          <div className="logo-section">
            <QrCode className="logo-icon" />
            <h1>QR Code Generator</h1>
          </div>
          <p className="subtitle">Create beautiful QR codes in 4 simple steps</p>
        </div>
      </header>

      <main className="main">
        <div className="container">
          {/* Step Progress */}
          <div className="step-progress">
            {steps.map((step) => {
              const Icon = step.icon
              return (
                <div
                  key={step.number}
                  className={`step ${currentStep >= step.number ? 'active' : ''} ${currentStep === step.number ? 'current' : ''}`}
                  onClick={() => setCurrentStep(step.number)}
                >
                  <div className="step-icon">
                    <Icon size={20} />
                  </div>
                  <span className="step-title">{step.title}</span>
                </div>
              )
            })}
          </div>

          <div className="content">
            {/* Left Panel - Form */}
            <div className="form-panel">
              {currentStep === 1 && (
                <div className="step-content">
                  <h2>Step 1: Enter Your Data</h2>
                  <p>Enter the text, URL, or data you want to encode in your QR code.</p>

                  <div className="form-group">
                    <label htmlFor="qr-data">QR Code Content</label>
                    <textarea
                      id="qr-data"
                      value={qrData}
                      onChange={(e) => setQrData(e.target.value)}
                      placeholder="Enter URL, text, or any data..."
                      rows={4}
                    />
                    <div className="quick-examples">
                      <p className="form-help">Quick examples:</p>
                      <div className="example-buttons">
                        <button
                          type="button"
                          className="example-btn"
                          onClick={() => setQrData('https://www.example.com')}
                        >
                          Website URL
                        </button>
                        <button
                          type="button"
                          className="example-btn"
                          onClick={() => setQrData('MUNCHIES')}
                        >
                          Text
                        </button>
                        <button
                          type="button"
                          className="example-btn"
                          onClick={() => setQrData('mailto:<EMAIL>')}
                        >
                          Email
                        </button>
                      </div>
                    </div>
                  </div>

                  <button
                    className="btn btn-primary"
                    onClick={() => setCurrentStep(2)}
                    disabled={!qrData.trim()}
                  >
                    Next: Choose Style
                  </button>
                </div>
              )}

              {currentStep === 2 && (
                <div className="step-content">
                  <h2>Step 2: Choose Colors & Style</h2>
                  <p>Customize the appearance and size of your QR code.</p>

                  <div className="style-controls">
                    <div className="color-controls">
                      <div className="form-group">
                        <label>QR Code Color</label>
                        <div className="color-picker-wrapper">
                          <button
                            className="color-button"
                            style={{ backgroundColor: qrColor }}
                            onClick={() => setShowColorPicker(!showColorPicker)}
                          />
                          {showColorPicker && (
                            <div className="color-picker-popover">
                              <div className="color-picker-cover" onClick={() => setShowColorPicker(false)} />
                              <ChromePicker
                                color={qrColor}
                                onChange={(color) => setQrColor(color.hex)}
                              />
                            </div>
                          )}
                        </div>
                      </div>

                      <div className="form-group">
                        <label>Background Color</label>
                        <div className="color-picker-wrapper">
                          <button
                            className="color-button"
                            style={{ backgroundColor: bgColor }}
                            onClick={() => setShowBgColorPicker(!showBgColorPicker)}
                          />
                          {showBgColorPicker && (
                            <div className="color-picker-popover">
                              <div className="color-picker-cover" onClick={() => setShowBgColorPicker(false)} />
                              <ChromePicker
                                color={bgColor}
                                onChange={(color) => setBgColor(color.hex)}
                              />
                            </div>
                          )}
                        </div>
                      </div>
                    </div>

                    <div className="size-control">
                      <div className="form-group">
                        <label htmlFor="qr-size">QR Code Size</label>
                        <div className="size-input-wrapper">
                          <input
                            id="qr-size"
                            type="range"
                            min="200"
                            max="800"
                            step="50"
                            value={qrSize}
                            onChange={(e) => setQrSize(parseInt(e.target.value))}
                            className="size-slider"
                          />
                          <div className="size-display">
                            <span className="size-value">{qrSize}px</span>
                            <div className="size-presets">
                              <button
                                type="button"
                                className={`size-preset ${qrSize === 200 ? 'active' : ''}`}
                                onClick={() => setQrSize(200)}
                              >
                                Small
                              </button>
                              <button
                                type="button"
                                className={`size-preset ${qrSize === 300 ? 'active' : ''}`}
                                onClick={() => setQrSize(300)}
                              >
                                Medium
                              </button>
                              <button
                                type="button"
                                className={`size-preset ${qrSize === 500 ? 'active' : ''}`}
                                onClick={() => setQrSize(500)}
                              >
                                Large
                              </button>
                              <button
                                type="button"
                                className={`size-preset ${qrSize === 800 ? 'active' : ''}`}
                                onClick={() => setQrSize(800)}
                              >
                                XL
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="button-group">
                    <button
                      className="btn btn-secondary"
                      onClick={() => setCurrentStep(1)}
                    >
                      Back
                    </button>
                    <button
                      className="btn btn-primary"
                      onClick={() => setCurrentStep(3)}
                    >
                      Next: Upload Logo
                    </button>
                  </div>
                </div>
              )}

              {currentStep === 3 && (
                <div className="step-content">
                  <h2>Step 3: Upload & Position Logo (Optional)</h2>
                  <p>Add your company logo and customize its position and size on the QR code.</p>

                  <div className="form-group">
                    <label>Company Logo</label>
                    <div className="upload-area">
                      <input
                        ref={fileInputRef}
                        type="file"
                        accept="image/*"
                        onChange={handleLogoUpload}
                        style={{ display: 'none' }}
                      />
                      <button
                        className="btn btn-upload"
                        onClick={() => fileInputRef.current?.click()}
                      >
                        <Upload size={20} />
                        {logo ? 'Change Logo' : 'Upload Logo'}
                      </button>
                      {logo && (
                        <div className="logo-preview">
                          <img src={logo} alt="Logo preview" />
                          <button
                            className="remove-logo"
                            onClick={() => setLogo(null)}
                          >
                            ×
                          </button>
                        </div>
                      )}
                    </div>
                  </div>

                  {logo && (
                    <div className="logo-controls">
                      <div className="logo-debug">
                        <p><strong>Logo Status:</strong> Loaded ✅</p>
                        <p><strong>Position:</strong> {logoPosition}</p>
                        <p><strong>Size:</strong> {logoSize}% ({Math.round((qrSize * logoSize) / 100)}px)</p>
                        <p><strong>QR Size:</strong> {qrSize}px</p>
                        <button
                          type="button"
                          className="btn btn-secondary"
                          onClick={() => {
                            console.log('Manual regenerate triggered')
                            generateQRCode()
                          }}
                          style={{ marginTop: '0.5rem', padding: '0.5rem 1rem', fontSize: '0.8rem' }}
                        >
                          🔄 Regenerate QR
                        </button>
                      </div>

                      <div className="form-group">
                        <label>Logo Position</label>
                        <p className="form-help">Choose where to place your logo on the QR code</p>
                        <div className="position-grid">
                          {[
                            { value: 'top-left', label: '⬉', title: 'Top Left', text: 'TL' },
                            { value: 'top-center', label: '⬆', title: 'Top Center', text: 'TC' },
                            { value: 'top-right', label: '⬈', title: 'Top Right', text: 'TR' },
                            { value: 'center-left', label: '⬅', title: 'Center Left', text: 'CL' },
                            { value: 'center', label: '⊙', title: 'Center', text: 'C' },
                            { value: 'center-right', label: '➡', title: 'Center Right', text: 'CR' },
                            { value: 'bottom-left', label: '⬋', title: 'Bottom Left', text: 'BL' },
                            { value: 'bottom-center', label: '⬇', title: 'Bottom Center', text: 'BC' },
                            { value: 'bottom-right', label: '⬊', title: 'Bottom Right', text: 'BR' }
                          ].map((position) => (
                            <button
                              key={position.value}
                              type="button"
                              className={`position-btn ${logoPosition === position.value ? 'active' : ''}`}
                              onClick={() => {
                                setLogoPosition(position.value)
                                console.log('Position changed to:', position.value)
                              }}
                              title={position.title}
                            >
                              <span className="position-icon">{position.label}</span>
                              <span className="position-text">{position.text}</span>
                            </button>
                          ))}
                        </div>
                      </div>

                      <div className="form-group">
                        <label htmlFor="logo-size">Logo Size</label>
                        <div className="logo-size-control">
                          <input
                            id="logo-size"
                            type="range"
                            min="10"
                            max="40"
                            step="2"
                            value={logoSize}
                            onChange={(e) => setLogoSize(parseInt(e.target.value))}
                            className="logo-size-slider"
                          />
                          <div className="logo-size-display">
                            <span className="logo-size-value">{logoSize}%</span>
                            <div className="logo-size-presets">
                              <button
                                type="button"
                                className={`logo-size-preset ${logoSize === 15 ? 'active' : ''}`}
                                onClick={() => setLogoSize(15)}
                              >
                                Small
                              </button>
                              <button
                                type="button"
                                className={`logo-size-preset ${logoSize === 20 ? 'active' : ''}`}
                                onClick={() => setLogoSize(20)}
                              >
                                Medium
                              </button>
                              <button
                                type="button"
                                className={`logo-size-preset ${logoSize === 30 ? 'active' : ''}`}
                                onClick={() => setLogoSize(30)}
                              >
                                Large
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  <div className="button-group">
                    <button
                      className="btn btn-secondary"
                      onClick={() => setCurrentStep(2)}
                    >
                      Back
                    </button>
                    <button
                      className="btn btn-primary"
                      onClick={() => setCurrentStep(4)}
                    >
                      Next: Download
                    </button>
                  </div>
                </div>
              )}

              {currentStep === 4 && (
                <div className="step-content">
                  <h2>Step 4: Download Your QR Code</h2>
                  <p>Your QR code is ready! Download it as a PNG image.</p>

                  <div className="download-section">
                    <button
                      className="btn btn-download"
                      onClick={downloadQR}
                      disabled={!qrData}
                    >
                      <Download size={20} />
                      Download PNG
                    </button>

                    <div className="qr-info">
                      <p><strong>Content:</strong> {qrData}</p>
                      <p><strong>Size:</strong> {qrSize}x{qrSize} pixels</p>
                      <p><strong>Format:</strong> PNG</p>
                    </div>
                  </div>

                  <div className="button-group">
                    <button
                      className="btn btn-secondary"
                      onClick={() => setCurrentStep(3)}
                    >
                      Back
                    </button>
                    <button
                      className="btn btn-primary"
                      onClick={() => {
                        setCurrentStep(1)
                        setQrData('')
                        setLogo(null)
                        setLogoPosition('center')
                        setLogoSize(20)
                        setQrColor('#000000')
                        setBgColor('#ffffff')
                        setQrSize(300)
                      }}
                    >
                      Create New QR
                    </button>
                  </div>
                </div>
              )}
            </div>

            {/* Right Panel - QR Preview */}
            <div className="preview-panel">
              <div className="preview-container">
                <div className="preview-header">
                  <h3>Live Preview</h3>
                  {qrData && (
                    <button
                      type="button"
                      className="refresh-btn"
                      onClick={() => {
                        console.log('Force refresh triggered')
                        generateQRCode()
                      }}
                      title="Refresh QR Code"
                    >
                      🔄
                    </button>
                  )}
                </div>
                <div className="qr-preview">
                  {qrData ? (
                    <div className="qr-display">
                      <canvas
                        ref={canvasRef}
                        width={qrSize}
                        height={qrSize}
                        className="qr-canvas"
                        style={{
                          maxWidth: Math.min(qrSize, 350) + 'px',
                          maxHeight: Math.min(qrSize, 350) + 'px',
                          border: '1px solid #e5e7eb',
                          borderRadius: '12px',
                          background: 'white'
                        }}
                      />
                    </div>
                  ) : (
                    <div className="qr-placeholder">
                      <QrCode size={80} />
                      <p>Enter data to see QR code preview</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}

export default App
