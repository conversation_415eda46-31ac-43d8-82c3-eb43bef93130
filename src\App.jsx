import { useState, useEffect, useRef } from 'react'
import QRCode from 'qrcode'
import { ChromePicker } from 'react-color'
import { Download, Upload, QrCode, Palette, Type, Image } from 'lucide-react'
import './App.css'

function App() {
  const [currentStep, setCurrentStep] = useState(1)
  const [qrData, setQrData] = useState('')
  const [qrColor, setQrColor] = useState('#000000')
  const [bgColor, setBgColor] = useState('#ffffff')
  const [logo, setLogo] = useState(null)
  const [qrCodeUrl, setQrCodeUrl] = useState('')
  const [showColorPicker, setShowColorPicker] = useState(false)
  const [showBgColorPicker, setShowBgColorPicker] = useState(false)
  const canvasRef = useRef(null)
  const fileInputRef = useRef(null)

  // Generate QR code whenever data or colors change
  useEffect(() => {
    if (qrData) {
      generateQRCode()
    }
  }, [qrData, qrColor, bgColor, logo])

  const generateQRCode = async () => {
    try {
      const canvas = canvasRef.current
      const ctx = canvas.getContext('2d')

      // Generate QR code
      await QRCode.toCanvas(canvas, qrData, {
        width: 300,
        margin: 2,
        color: {
          dark: qrColor,
          light: bgColor
        }
      })

      // Add logo if present
      if (logo) {
        const img = new Image()
        img.onload = () => {
          const logoSize = 60
          const x = (canvas.width - logoSize) / 2
          const y = (canvas.height - logoSize) / 2

          // Draw white background circle for logo
          ctx.fillStyle = '#ffffff'
          ctx.beginPath()
          ctx.arc(canvas.width / 2, canvas.height / 2, logoSize / 2 + 5, 0, 2 * Math.PI)
          ctx.fill()

          // Draw logo
          ctx.drawImage(img, x, y, logoSize, logoSize)

          // Update QR code URL
          setQrCodeUrl(canvas.toDataURL())
        }
        img.src = logo
      } else {
        setQrCodeUrl(canvas.toDataURL())
      }
    } catch (error) {
      console.error('Error generating QR code:', error)
    }
  }

  const handleLogoUpload = (event) => {
    const file = event.target.files[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = (e) => {
        setLogo(e.target.result)
      }
      reader.readAsDataURL(file)
    }
  }

  const downloadQR = () => {
    const link = document.createElement('a')
    link.download = 'qrcode.png'
    link.href = qrCodeUrl
    link.click()
  }

  const steps = [
    { number: 1, title: 'Enter Data', icon: Type },
    { number: 2, title: 'Choose Style', icon: Palette },
    { number: 3, title: 'Upload Logo', icon: Image },
    { number: 4, title: 'Download QR', icon: Download }
  ]

  return (
    <div className="app">
      <header className="header">
        <div className="container">
          <div className="logo-section">
            <QrCode className="logo-icon" />
            <h1>QR Code Generator</h1>
          </div>
          <p className="subtitle">Create beautiful QR codes in 4 simple steps</p>
        </div>
      </header>

      <main className="main">
        <div className="container">
          {/* Step Progress */}
          <div className="step-progress">
            {steps.map((step) => {
              const Icon = step.icon
              return (
                <div
                  key={step.number}
                  className={`step ${currentStep >= step.number ? 'active' : ''} ${currentStep === step.number ? 'current' : ''}`}
                  onClick={() => setCurrentStep(step.number)}
                >
                  <div className="step-icon">
                    <Icon size={20} />
                  </div>
                  <span className="step-title">{step.title}</span>
                </div>
              )
            })}
          </div>

          <div className="content">
            {/* Left Panel - Form */}
            <div className="form-panel">
              {currentStep === 1 && (
                <div className="step-content">
                  <h2>Step 1: Enter Your Data</h2>
                  <p>Enter the text, URL, or data you want to encode in your QR code.</p>

                  <div className="form-group">
                    <label htmlFor="qr-data">QR Code Content</label>
                    <textarea
                      id="qr-data"
                      value={qrData}
                      onChange={(e) => setQrData(e.target.value)}
                      placeholder="Enter URL, text, or any data..."
                      rows={4}
                    />
                  </div>

                  <button
                    className="btn btn-primary"
                    onClick={() => setCurrentStep(2)}
                    disabled={!qrData.trim()}
                  >
                    Next: Choose Style
                  </button>
                </div>
              )}

              {currentStep === 2 && (
                <div className="step-content">
                  <h2>Step 2: Choose Colors & Style</h2>
                  <p>Customize the appearance of your QR code with colors.</p>

                  <div className="color-controls">
                    <div className="form-group">
                      <label>QR Code Color</label>
                      <div className="color-picker-wrapper">
                        <button
                          className="color-button"
                          style={{ backgroundColor: qrColor }}
                          onClick={() => setShowColorPicker(!showColorPicker)}
                        />
                        {showColorPicker && (
                          <div className="color-picker-popover">
                            <div className="color-picker-cover" onClick={() => setShowColorPicker(false)} />
                            <ChromePicker
                              color={qrColor}
                              onChange={(color) => setQrColor(color.hex)}
                            />
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="form-group">
                      <label>Background Color</label>
                      <div className="color-picker-wrapper">
                        <button
                          className="color-button"
                          style={{ backgroundColor: bgColor }}
                          onClick={() => setShowBgColorPicker(!showBgColorPicker)}
                        />
                        {showBgColorPicker && (
                          <div className="color-picker-popover">
                            <div className="color-picker-cover" onClick={() => setShowBgColorPicker(false)} />
                            <ChromePicker
                              color={bgColor}
                              onChange={(color) => setBgColor(color.hex)}
                            />
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="button-group">
                    <button
                      className="btn btn-secondary"
                      onClick={() => setCurrentStep(1)}
                    >
                      Back
                    </button>
                    <button
                      className="btn btn-primary"
                      onClick={() => setCurrentStep(3)}
                    >
                      Next: Upload Logo
                    </button>
                  </div>
                </div>
              )}

              {currentStep === 3 && (
                <div className="step-content">
                  <h2>Step 3: Upload Logo (Optional)</h2>
                  <p>Add your company logo to the center of the QR code.</p>

                  <div className="form-group">
                    <label>Company Logo</label>
                    <div className="upload-area">
                      <input
                        ref={fileInputRef}
                        type="file"
                        accept="image/*"
                        onChange={handleLogoUpload}
                        style={{ display: 'none' }}
                      />
                      <button
                        className="btn btn-upload"
                        onClick={() => fileInputRef.current?.click()}
                      >
                        <Upload size={20} />
                        {logo ? 'Change Logo' : 'Upload Logo'}
                      </button>
                      {logo && (
                        <div className="logo-preview">
                          <img src={logo} alt="Logo preview" />
                          <button
                            className="remove-logo"
                            onClick={() => setLogo(null)}
                          >
                            ×
                          </button>
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="button-group">
                    <button
                      className="btn btn-secondary"
                      onClick={() => setCurrentStep(2)}
                    >
                      Back
                    </button>
                    <button
                      className="btn btn-primary"
                      onClick={() => setCurrentStep(4)}
                    >
                      Next: Download
                    </button>
                  </div>
                </div>
              )}

              {currentStep === 4 && (
                <div className="step-content">
                  <h2>Step 4: Download Your QR Code</h2>
                  <p>Your QR code is ready! Download it as a PNG image.</p>

                  <div className="download-section">
                    <button
                      className="btn btn-download"
                      onClick={downloadQR}
                      disabled={!qrCodeUrl}
                    >
                      <Download size={20} />
                      Download PNG
                    </button>

                    <div className="qr-info">
                      <p><strong>Content:</strong> {qrData}</p>
                      <p><strong>Size:</strong> 300x300 pixels</p>
                      <p><strong>Format:</strong> PNG</p>
                    </div>
                  </div>

                  <div className="button-group">
                    <button
                      className="btn btn-secondary"
                      onClick={() => setCurrentStep(3)}
                    >
                      Back
                    </button>
                    <button
                      className="btn btn-primary"
                      onClick={() => {
                        setCurrentStep(1)
                        setQrData('')
                        setLogo(null)
                        setQrColor('#000000')
                        setBgColor('#ffffff')
                      }}
                    >
                      Create New QR
                    </button>
                  </div>
                </div>
              )}
            </div>

            {/* Right Panel - QR Preview */}
            <div className="preview-panel">
              <div className="preview-container">
                <h3>Live Preview</h3>
                <div className="qr-preview">
                  {qrData ? (
                    <div className="qr-display">
                      <canvas
                        ref={canvasRef}
                        style={{ display: 'none' }}
                      />
                      {qrCodeUrl && (
                        <img
                          src={qrCodeUrl}
                          alt="QR Code Preview"
                          className="qr-image"
                        />
                      )}
                    </div>
                  ) : (
                    <div className="qr-placeholder">
                      <QrCode size={80} />
                      <p>Enter data to see QR code preview</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}

export default App
