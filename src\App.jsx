import { useState, useEffect, useRef } from 'react'
import QRCode from 'qrcode'
import { ChromePicker } from 'react-color'
import { Download, Upload, QrCode, Palette, Type, Image } from 'lucide-react'
import './App.css'

function App() {
  const [currentStep, setCurrentStep] = useState(1)
  const [qrData, setQrData] = useState('')
  const [qrColor, setQrColor] = useState('#000000')
  const [bgColor, setBgColor] = useState('#ffffff')
  const [qrSize, setQrSize] = useState(300)
  const [logo, setLogo] = useState(null)
  const [logoPosition, setLogoPosition] = useState('center')
  const [logoSize, setLogoSize] = useState(20) // Percentage of QR code size
  const [qrCodeUrl, setQrCodeUrl] = useState('')
  const [showColorPicker, setShowColorPicker] = useState(false)
  const [showBgColorPicker, setShowBgColorPicker] = useState(false)
  const canvasRef = useRef(null)
  const fileInputRef = useRef(null)

  // Generate QR code whenever data, colors, size, or logo settings change
  useEffect(() => {
    if (qrData) {
      generateQRCode()
    }
  }, [qrData, qrColor, bgColor, qrSize, logo, logoPosition, logoSize])

  const generateQRCode = async () => {
    try {
      const canvas = canvasRef.current
      if (!canvas) return

      // Set canvas size
      canvas.width = qrSize
      canvas.height = qrSize

      const ctx = canvas.getContext('2d')

      // Clear canvas first
      ctx.clearRect(0, 0, canvas.width, canvas.height)

      // Generate QR code first
      await QRCode.toCanvas(canvas, qrData, {
        width: qrSize,
        margin: 2,
        color: {
          dark: qrColor,
          light: bgColor
        }
      })

      // Update QR code URL for download
      setQrCodeUrl(canvas.toDataURL('image/png', 1.0))

      // Add logo if present
      if (logo) {
        const img = new Image()
        img.crossOrigin = 'anonymous' // Handle CORS issues

        img.onload = () => {
          try {
            const actualLogoSize = Math.max(20, (qrSize * logoSize) / 100) // Minimum 20px
            const padding = 15 // Padding from edges

            console.log('Drawing logo:', {
              logoSize: actualLogoSize,
              canvasSize: { width: canvas.width, height: canvas.height },
              position: logoPosition
            })

            // Calculate logo position based on logoPosition
            let x, y
            switch (logoPosition) {
              case 'top-left':
                x = padding
                y = padding
                break
              case 'top-center':
                x = (canvas.width - actualLogoSize) / 2
                y = padding
                break
              case 'top-right':
                x = canvas.width - actualLogoSize - padding
                y = padding
                break
              case 'center-left':
                x = padding
                y = (canvas.height - actualLogoSize) / 2
                break
              case 'center':
                x = (canvas.width - actualLogoSize) / 2
                y = (canvas.height - actualLogoSize) / 2
                break
              case 'center-right':
                x = canvas.width - actualLogoSize - padding
                y = (canvas.height - actualLogoSize) / 2
                break
              case 'bottom-left':
                x = padding
                y = canvas.height - actualLogoSize - padding
                break
              case 'bottom-center':
                x = (canvas.width - actualLogoSize) / 2
                y = canvas.height - actualLogoSize - padding
                break
              case 'bottom-right':
                x = canvas.width - actualLogoSize - padding
                y = canvas.height - actualLogoSize - padding
                break
              default:
                x = (canvas.width - actualLogoSize) / 2
                y = (canvas.height - actualLogoSize) / 2
            }

            // Save context state
            ctx.save()

            // Draw background for logo
            if (logoPosition === 'center') {
              // Circular background for center position
              ctx.fillStyle = '#ffffff'
              ctx.beginPath()
              ctx.arc(x + actualLogoSize / 2, y + actualLogoSize / 2, actualLogoSize / 2 + 8, 0, 2 * Math.PI)
              ctx.fill()

              // Add border
              ctx.strokeStyle = '#e5e7eb'
              ctx.lineWidth = 2
              ctx.stroke()
            } else {
              // Rounded rectangle background for other positions
              const bgPadding = 6
              ctx.fillStyle = 'rgba(255, 255, 255, 0.95)'
              ctx.fillRect(x - bgPadding, y - bgPadding, actualLogoSize + (bgPadding * 2), actualLogoSize + (bgPadding * 2))

              // Add border
              ctx.strokeStyle = 'rgba(0, 0, 0, 0.1)'
              ctx.lineWidth = 1
              ctx.strokeRect(x - bgPadding, y - bgPadding, actualLogoSize + (bgPadding * 2), actualLogoSize + (bgPadding * 2))
            }

            // Draw logo with rounded corners
            ctx.save()
            ctx.beginPath()
            const radius = 8
            ctx.moveTo(x + radius, y)
            ctx.lineTo(x + actualLogoSize - radius, y)
            ctx.quadraticCurveTo(x + actualLogoSize, y, x + actualLogoSize, y + radius)
            ctx.lineTo(x + actualLogoSize, y + actualLogoSize - radius)
            ctx.quadraticCurveTo(x + actualLogoSize, y + actualLogoSize, x + actualLogoSize - radius, y + actualLogoSize)
            ctx.lineTo(x + radius, y + actualLogoSize)
            ctx.quadraticCurveTo(x, y + actualLogoSize, x, y + actualLogoSize - radius)
            ctx.lineTo(x, y + radius)
            ctx.quadraticCurveTo(x, y, x + radius, y)
            ctx.closePath()
            ctx.clip()

            // Draw the logo image
            ctx.drawImage(img, x, y, actualLogoSize, actualLogoSize)
            ctx.restore()

            // Restore context state
            ctx.restore()

            // Update QR code URL with logo for download
            setQrCodeUrl(canvas.toDataURL('image/png', 1.0))

            console.log('Logo drawn successfully at position:', logoPosition, 'size:', actualLogoSize)
          } catch (logoError) {
            console.error('Error drawing logo:', logoError)
            // Keep the QR code URL as is
          }
        }

        img.onerror = (error) => {
          console.error('Error loading logo image:', error)
          // Keep the QR code URL as is
        }

        // Set image source (this triggers the load)
        img.src = logo
      }
    } catch (error) {
      console.error('Error generating QR code:', error)
    }
  }

  const handleLogoUpload = (event) => {
    const file = event.target.files[0]
    if (file) {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        alert('Please select a valid image file')
        return
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        alert('Image file size should be less than 5MB')
        return
      }

      const reader = new FileReader()
      reader.onload = (e) => {
        const img = new Image()
        img.onload = () => {
          console.log('Logo image loaded successfully:', {
            width: img.width,
            height: img.height,
            src: e.target.result.substring(0, 50) + '...'
          })
          setLogo(e.target.result)
        }
        img.onerror = () => {
          console.error('Failed to load logo image')
          alert('Failed to load the selected image. Please try another image.')
        }
        img.src = e.target.result
      }
      reader.onerror = () => {
        console.error('Failed to read file')
        alert('Failed to read the selected file. Please try again.')
      }
      reader.readAsDataURL(file)
    }
  }

  const downloadQR = () => {
    const canvas = canvasRef.current
    if (canvas) {
      const link = document.createElement('a')
      link.download = `qrcode-${qrSize}x${qrSize}.png`
      link.href = canvas.toDataURL('image/png', 1.0)
      link.click()
    }
  }

  const steps = [
    { number: 1, title: 'Enter Data', icon: Type },
    { number: 2, title: 'Choose Style', icon: Palette },
    { number: 3, title: 'Upload Logo', icon: Image },
    { number: 4, title: 'Download QR', icon: Download }
  ]

  return (
    <div className="app">
      <header className="header">
        <div className="container">
          <div className="logo-section">
            <QrCode className="logo-icon" />
            <h1>QR Code Generator</h1>
          </div>
          <p className="subtitle">Create beautiful QR codes in 4 simple steps</p>
        </div>
      </header>

      <main className="main">
        <div className="container">
          {/* Step Progress */}
          <div className="step-progress">
            {steps.map((step) => {
              const Icon = step.icon
              return (
                <div
                  key={step.number}
                  className={`step ${currentStep >= step.number ? 'active' : ''} ${currentStep === step.number ? 'current' : ''}`}
                  onClick={() => setCurrentStep(step.number)}
                >
                  <div className="step-icon">
                    <Icon size={20} />
                  </div>
                  <span className="step-title">{step.title}</span>
                </div>
              )
            })}
          </div>

          <div className="content">
            {/* Left Panel - Form */}
            <div className="form-panel">
              {currentStep === 1 && (
                <div className="step-content">
                  <h2>Step 1: Enter Your Data</h2>
                  <p>Enter the text, URL, or data you want to encode in your QR code.</p>

                  <div className="form-group">
                    <label htmlFor="qr-data">QR Code Content</label>
                    <textarea
                      id="qr-data"
                      value={qrData}
                      onChange={(e) => setQrData(e.target.value)}
                      placeholder="Enter URL, text, or any data..."
                      rows={4}
                    />
                  </div>

                  <button
                    className="btn btn-primary"
                    onClick={() => setCurrentStep(2)}
                    disabled={!qrData.trim()}
                  >
                    Next: Choose Style
                  </button>
                </div>
              )}

              {currentStep === 2 && (
                <div className="step-content">
                  <h2>Step 2: Choose Colors & Style</h2>
                  <p>Customize the appearance and size of your QR code.</p>

                  <div className="style-controls">
                    <div className="color-controls">
                      <div className="form-group">
                        <label>QR Code Color</label>
                        <div className="color-picker-wrapper">
                          <button
                            className="color-button"
                            style={{ backgroundColor: qrColor }}
                            onClick={() => setShowColorPicker(!showColorPicker)}
                          />
                          {showColorPicker && (
                            <div className="color-picker-popover">
                              <div className="color-picker-cover" onClick={() => setShowColorPicker(false)} />
                              <ChromePicker
                                color={qrColor}
                                onChange={(color) => setQrColor(color.hex)}
                              />
                            </div>
                          )}
                        </div>
                      </div>

                      <div className="form-group">
                        <label>Background Color</label>
                        <div className="color-picker-wrapper">
                          <button
                            className="color-button"
                            style={{ backgroundColor: bgColor }}
                            onClick={() => setShowBgColorPicker(!showBgColorPicker)}
                          />
                          {showBgColorPicker && (
                            <div className="color-picker-popover">
                              <div className="color-picker-cover" onClick={() => setShowBgColorPicker(false)} />
                              <ChromePicker
                                color={bgColor}
                                onChange={(color) => setBgColor(color.hex)}
                              />
                            </div>
                          )}
                        </div>
                      </div>
                    </div>

                    <div className="size-control">
                      <div className="form-group">
                        <label htmlFor="qr-size">QR Code Size</label>
                        <div className="size-input-wrapper">
                          <input
                            id="qr-size"
                            type="range"
                            min="200"
                            max="800"
                            step="50"
                            value={qrSize}
                            onChange={(e) => setQrSize(parseInt(e.target.value))}
                            className="size-slider"
                          />
                          <div className="size-display">
                            <span className="size-value">{qrSize}px</span>
                            <div className="size-presets">
                              <button
                                type="button"
                                className={`size-preset ${qrSize === 200 ? 'active' : ''}`}
                                onClick={() => setQrSize(200)}
                              >
                                Small
                              </button>
                              <button
                                type="button"
                                className={`size-preset ${qrSize === 300 ? 'active' : ''}`}
                                onClick={() => setQrSize(300)}
                              >
                                Medium
                              </button>
                              <button
                                type="button"
                                className={`size-preset ${qrSize === 500 ? 'active' : ''}`}
                                onClick={() => setQrSize(500)}
                              >
                                Large
                              </button>
                              <button
                                type="button"
                                className={`size-preset ${qrSize === 800 ? 'active' : ''}`}
                                onClick={() => setQrSize(800)}
                              >
                                XL
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="button-group">
                    <button
                      className="btn btn-secondary"
                      onClick={() => setCurrentStep(1)}
                    >
                      Back
                    </button>
                    <button
                      className="btn btn-primary"
                      onClick={() => setCurrentStep(3)}
                    >
                      Next: Upload Logo
                    </button>
                  </div>
                </div>
              )}

              {currentStep === 3 && (
                <div className="step-content">
                  <h2>Step 3: Upload & Position Logo (Optional)</h2>
                  <p>Add your company logo and customize its position and size on the QR code.</p>

                  <div className="form-group">
                    <label>Company Logo</label>
                    <div className="upload-area">
                      <input
                        ref={fileInputRef}
                        type="file"
                        accept="image/*"
                        onChange={handleLogoUpload}
                        style={{ display: 'none' }}
                      />
                      <button
                        className="btn btn-upload"
                        onClick={() => fileInputRef.current?.click()}
                      >
                        <Upload size={20} />
                        {logo ? 'Change Logo' : 'Upload Logo'}
                      </button>
                      {logo && (
                        <div className="logo-preview">
                          <img src={logo} alt="Logo preview" />
                          <button
                            className="remove-logo"
                            onClick={() => setLogo(null)}
                          >
                            ×
                          </button>
                        </div>
                      )}
                    </div>
                  </div>

                  {logo && (
                    <div className="logo-controls">
                      <div className="logo-debug">
                        <p><strong>Logo Status:</strong> Loaded ✅</p>
                        <p><strong>Position:</strong> {logoPosition}</p>
                        <p><strong>Size:</strong> {logoSize}% ({Math.round((qrSize * logoSize) / 100)}px)</p>
                        <p><strong>QR Size:</strong> {qrSize}px</p>
                        <button
                          type="button"
                          className="btn btn-secondary"
                          onClick={() => {
                            console.log('Manual regenerate triggered')
                            generateQRCode()
                          }}
                          style={{ marginTop: '0.5rem', padding: '0.5rem 1rem', fontSize: '0.8rem' }}
                        >
                          🔄 Regenerate QR
                        </button>
                      </div>

                      <div className="form-group">
                        <label>Logo Position</label>
                        <div className="position-grid">
                          {[
                            { value: 'top-left', label: '↖️', title: 'Top Left' },
                            { value: 'top-center', label: '⬆️', title: 'Top Center' },
                            { value: 'top-right', label: '↗️', title: 'Top Right' },
                            { value: 'center-left', label: '⬅️', title: 'Center Left' },
                            { value: 'center', label: '⭕', title: 'Center' },
                            { value: 'center-right', label: '➡️', title: 'Center Right' },
                            { value: 'bottom-left', label: '↙️', title: 'Bottom Left' },
                            { value: 'bottom-center', label: '⬇️', title: 'Bottom Center' },
                            { value: 'bottom-right', label: '↘️', title: 'Bottom Right' }
                          ].map((position) => (
                            <button
                              key={position.value}
                              type="button"
                              className={`position-btn ${logoPosition === position.value ? 'active' : ''}`}
                              onClick={() => setLogoPosition(position.value)}
                              title={position.title}
                            >
                              {position.label}
                            </button>
                          ))}
                        </div>
                      </div>

                      <div className="form-group">
                        <label htmlFor="logo-size">Logo Size</label>
                        <div className="logo-size-control">
                          <input
                            id="logo-size"
                            type="range"
                            min="10"
                            max="40"
                            step="2"
                            value={logoSize}
                            onChange={(e) => setLogoSize(parseInt(e.target.value))}
                            className="logo-size-slider"
                          />
                          <div className="logo-size-display">
                            <span className="logo-size-value">{logoSize}%</span>
                            <div className="logo-size-presets">
                              <button
                                type="button"
                                className={`logo-size-preset ${logoSize === 15 ? 'active' : ''}`}
                                onClick={() => setLogoSize(15)}
                              >
                                Small
                              </button>
                              <button
                                type="button"
                                className={`logo-size-preset ${logoSize === 20 ? 'active' : ''}`}
                                onClick={() => setLogoSize(20)}
                              >
                                Medium
                              </button>
                              <button
                                type="button"
                                className={`logo-size-preset ${logoSize === 30 ? 'active' : ''}`}
                                onClick={() => setLogoSize(30)}
                              >
                                Large
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  <div className="button-group">
                    <button
                      className="btn btn-secondary"
                      onClick={() => setCurrentStep(2)}
                    >
                      Back
                    </button>
                    <button
                      className="btn btn-primary"
                      onClick={() => setCurrentStep(4)}
                    >
                      Next: Download
                    </button>
                  </div>
                </div>
              )}

              {currentStep === 4 && (
                <div className="step-content">
                  <h2>Step 4: Download Your QR Code</h2>
                  <p>Your QR code is ready! Download it as a PNG image.</p>

                  <div className="download-section">
                    <button
                      className="btn btn-download"
                      onClick={downloadQR}
                      disabled={!qrData}
                    >
                      <Download size={20} />
                      Download PNG
                    </button>

                    <div className="qr-info">
                      <p><strong>Content:</strong> {qrData}</p>
                      <p><strong>Size:</strong> {qrSize}x{qrSize} pixels</p>
                      <p><strong>Format:</strong> PNG</p>
                    </div>
                  </div>

                  <div className="button-group">
                    <button
                      className="btn btn-secondary"
                      onClick={() => setCurrentStep(3)}
                    >
                      Back
                    </button>
                    <button
                      className="btn btn-primary"
                      onClick={() => {
                        setCurrentStep(1)
                        setQrData('')
                        setLogo(null)
                        setLogoPosition('center')
                        setLogoSize(20)
                        setQrColor('#000000')
                        setBgColor('#ffffff')
                        setQrSize(300)
                      }}
                    >
                      Create New QR
                    </button>
                  </div>
                </div>
              )}
            </div>

            {/* Right Panel - QR Preview */}
            <div className="preview-panel">
              <div className="preview-container">
                <h3>Live Preview</h3>
                <div className="qr-preview">
                  {qrData ? (
                    <div className="qr-display">
                      <canvas
                        ref={canvasRef}
                        width={qrSize}
                        height={qrSize}
                        className="qr-canvas"
                        style={{
                          maxWidth: Math.min(qrSize, 350) + 'px',
                          maxHeight: Math.min(qrSize, 350) + 'px',
                          border: '1px solid #e5e7eb',
                          borderRadius: '12px',
                          background: 'white'
                        }}
                      />
                    </div>
                  ) : (
                    <div className="qr-placeholder">
                      <QrCode size={80} />
                      <p>Enter data to see QR code preview</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}

export default App
