/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  color: #333;
}

.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Header */
.header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding: 2rem 0;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.logo-section {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 0.5rem;
}

.logo-icon {
  color: #4f46e5;
  width: 2.5rem;
  height: 2.5rem;
}

.header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0;
}

.subtitle {
  text-align: center;
  color: #6b7280;
  font-size: 1.1rem;
  margin: 0;
}

/* Main content */
.main {
  flex: 1;
  padding: 3rem 0;
}

/* Step progress */
.step-progress {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-bottom: 3rem;
  flex-wrap: wrap;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border: 2px solid transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 120px;
}

.step:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.step.active {
  border-color: #4f46e5;
  background: rgba(79, 70, 229, 0.1);
}

.step.current {
  background: #4f46e5;
  color: white;
}

.step-icon {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  background: rgba(79, 70, 229, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.step.current .step-icon {
  background: rgba(255, 255, 255, 0.2);
}

.step-title {
  font-weight: 600;
  font-size: 0.9rem;
  text-align: center;
}

/* Content layout */
.content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  align-items: start;
}

/* Form panel */
.form-panel {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 2.5rem;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.step-content h2 {
  font-size: 1.8rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.step-content p {
  color: #6b7280;
  margin-bottom: 2rem;
  line-height: 1.6;
}

/* Form elements */
.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
}

.form-group textarea,
.form-group input {
  width: 100%;
  padding: 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: white;
}

.form-group textarea:focus,
.form-group input:focus {
  outline: none;
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 120px;
  font-family: inherit;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  justify-content: center;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(79, 70, 229, 0.3);
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(79, 70, 229, 0.4);
}

.btn-secondary {
  background: #f3f4f6;
  color: #374151;
  border: 2px solid #e5e7eb;
}

.btn-secondary:hover {
  background: #e5e7eb;
  transform: translateY(-1px);
}

.btn-upload {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.btn-upload:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
}

.btn-download {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
  font-size: 1.1rem;
  padding: 1.2rem 2.5rem;
}

.btn-download:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4);
}

.button-group {
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
}

/* Style controls */
.style-controls {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.color-controls {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

/* Size control */
.size-control {
  padding: 1.5rem;
  background: #f9fafb;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
}

.size-input-wrapper {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.size-slider {
  width: 100%;
  height: 8px;
  border-radius: 4px;
  background: #e5e7eb;
  outline: none;
  -webkit-appearance: none;
  appearance: none;
}

.size-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #4f46e5;
  cursor: pointer;
  box-shadow: 0 2px 6px rgba(79, 70, 229, 0.3);
}

.size-slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #4f46e5;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 6px rgba(79, 70, 229, 0.3);
}

.size-display {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.size-value {
  font-weight: 600;
  font-size: 1.1rem;
  color: #4f46e5;
  background: rgba(79, 70, 229, 0.1);
  padding: 0.5rem 1rem;
  border-radius: 8px;
}

.size-presets {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.size-preset {
  padding: 0.5rem 1rem;
  border: 2px solid #e5e7eb;
  background: white;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.size-preset:hover {
  border-color: #4f46e5;
  background: rgba(79, 70, 229, 0.05);
}

.size-preset.active {
  border-color: #4f46e5;
  background: #4f46e5;
  color: white;
}

.color-picker-wrapper {
  position: relative;
}

.color-button {
  width: 60px;
  height: 40px;
  border: 3px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.color-button:hover {
  border-color: #4f46e5;
  transform: scale(1.05);
}

.color-picker-popover {
  position: absolute;
  top: 50px;
  left: 0;
  z-index: 1000;
}

.color-picker-cover {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
}

/* Upload area */
.upload-area {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.logo-preview {
  position: relative;
  display: inline-block;
  margin-top: 1rem;
}

.logo-preview img {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 8px;
  border: 2px solid #e5e7eb;
}

.remove-logo {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #ef4444;
  color: white;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  line-height: 1;
}

/* Download section */
.download-section {
  text-align: center;
  margin-bottom: 2rem;
}

.qr-info {
  margin-top: 1.5rem;
  padding: 1.5rem;
  background: #f9fafb;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
}

.qr-info p {
  margin: 0.5rem 0;
  color: #374151;
}

/* Preview panel */
.preview-panel {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 2.5rem;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: sticky;
  top: 2rem;
}

.preview-container h3 {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 1.5rem;
  text-align: center;
}

.qr-preview {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  background: #f9fafb;
  border-radius: 16px;
  border: 2px dashed #d1d5db;
  transition: all 0.3s ease;
  padding: 1rem;
}

.qr-display {
  display: flex;
  justify-content: center;
  align-items: center;
}

.qr-image {
  border-radius: 12px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  background: white;
  padding: 1rem;
  transition: all 0.3s ease;
}

.qr-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  color: #9ca3af;
  text-align: center;
}

.qr-placeholder svg {
  opacity: 0.5;
}

/* Responsive design */
@media (max-width: 1024px) {
  .content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .preview-panel {
    position: static;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 1rem;
  }

  .header {
    padding: 1.5rem 0;
  }

  .header h1 {
    font-size: 2rem;
  }

  .main {
    padding: 2rem 0;
  }

  .step-progress {
    gap: 1rem;
  }

  .step {
    min-width: 100px;
    padding: 0.8rem;
  }

  .step-icon {
    width: 2.5rem;
    height: 2.5rem;
  }

  .form-panel,
  .preview-panel {
    padding: 1.5rem;
  }

  .color-controls {
    grid-template-columns: 1fr;
  }

  .size-display {
    flex-direction: column;
    align-items: stretch;
  }

  .size-presets {
    justify-content: center;
  }

  .button-group {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .step-progress {
    flex-direction: column;
    align-items: center;
  }

  .step {
    flex-direction: row;
    min-width: 200px;
    justify-content: flex-start;
  }

  .step-icon {
    width: 2rem;
    height: 2rem;
  }
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.step-content {
  animation: fadeIn 0.5s ease-out;
}

/* Focus styles for accessibility */
.btn:focus,
.color-button:focus,
.step:focus {
  outline: 2px solid #4f46e5;
  outline-offset: 2px;
}

/* Loading states */
.btn:disabled {
  position: relative;
  overflow: hidden;
}

.btn:disabled::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}
