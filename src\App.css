/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
  min-height: 100vh;
  color: #333;
  overflow-x: hidden;
}

.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
}

.app::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
  pointer-events: none;
  z-index: -1;
}

/* Floating particles */
.particles {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
}

.particle {
  position: absolute;
  border-radius: 50%;
  animation: float 20s infinite linear;
}

.particle-0 {
  width: 4px;
  height: 4px;
  background: rgba(0, 212, 255, 0.6);
  left: 10%;
  animation-delay: 0s;
  animation-duration: 20s;
}

.particle-1 {
  width: 6px;
  height: 6px;
  background: rgba(255, 107, 157, 0.6);
  left: 30%;
  animation-delay: -5s;
  animation-duration: 25s;
}

.particle-2 {
  width: 3px;
  height: 3px;
  background: rgba(196, 113, 237, 0.6);
  left: 50%;
  animation-delay: -10s;
  animation-duration: 30s;
}

@keyframes float {
  0% {
    transform: translateY(100vh) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100px) rotate(360deg);
    opacity: 0;
  }
}

/* Header */
.header {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: 2.5rem 0;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  position: relative;
}

.header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  border-radius: 0 0 2rem 2rem;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.logo-section {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1.5rem;
  margin-bottom: 1rem;
  position: relative;
  z-index: 1;
}

.logo-icon {
  color: #00d4ff;
  width: 3.5rem;
  height: 3.5rem;
  filter: drop-shadow(0 0 20px rgba(0, 212, 255, 0.5));
  animation: pulse 2s ease-in-out infinite alternate;
}

@keyframes pulse {
  from {
    filter: drop-shadow(0 0 20px rgba(0, 212, 255, 0.5));
  }
  to {
    filter: drop-shadow(0 0 30px rgba(0, 212, 255, 0.8));
  }
}

.header h1 {
  font-size: 3.5rem;
  font-weight: 800;
  background: linear-gradient(135deg, #00d4ff 0%, #ff6b9d 50%, #c471ed 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
  text-shadow: 0 0 30px rgba(0, 212, 255, 0.3);
  letter-spacing: -0.02em;
}

.subtitle {
  text-align: center;
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.3rem;
  margin: 0;
  font-weight: 400;
  position: relative;
  z-index: 1;
}

/* Main content */
.main {
  flex: 1;
  padding: 3rem 0;
}

/* Step progress */
.step-progress {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 4rem;
  flex-wrap: wrap;
  position: relative;
}

.step-progress::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  height: 2px;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.2) 20%, rgba(255, 255, 255, 0.2) 80%, transparent 100%);
  z-index: 0;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.8rem;
  padding: 1.5rem 1rem;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  min-width: 140px;
  position: relative;
  z-index: 1;
}

.step:hover {
  transform: translateY(-8px) scale(1.05);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  background: rgba(255, 255, 255, 0.15);
}

.step.active {
  border-color: rgba(0, 212, 255, 0.5);
  background: rgba(0, 212, 255, 0.1);
  box-shadow: 0 0 30px rgba(0, 212, 255, 0.3);
}

.step.current {
  background: linear-gradient(135deg, #00d4ff 0%, #ff6b9d 100%);
  color: white;
  transform: translateY(-8px) scale(1.1);
  box-shadow: 0 20px 40px rgba(0, 212, 255, 0.4);
}

.step-icon {
  width: 3.5rem;
  height: 3.5rem;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.4s ease;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.step.current .step-icon {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
}

.step.active .step-icon {
  background: rgba(0, 212, 255, 0.2);
  border-color: rgba(0, 212, 255, 0.5);
}

.step-title {
  font-weight: 700;
  font-size: 0.95rem;
  text-align: center;
  color: rgba(255, 255, 255, 0.9);
}

.step.current .step-title {
  color: white;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

/* Content layout */
.content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  align-items: start;
}

/* Form panel */
.form-panel {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 3rem;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.form-panel::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  border-radius: 24px;
  z-index: -1;
}

.step-content {
  position: relative;
  z-index: 1;
}

.step-content h2 {
  font-size: 2.2rem;
  font-weight: 800;
  background: linear-gradient(135deg, #00d4ff 0%, #ff6b9d 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 0.8rem;
  letter-spacing: -0.01em;
}

.step-content p {
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 2.5rem;
  line-height: 1.7;
  font-size: 1.1rem;
}

/* Form elements */
.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  font-weight: 700;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 0.8rem;
  font-size: 1.1rem;
}

.form-group textarea,
.form-group input {
  width: 100%;
  padding: 1.2rem;
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  color: rgba(255, 255, 255, 0.9);
}

.form-group textarea::placeholder,
.form-group input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.form-group textarea:focus,
.form-group input:focus {
  outline: none;
  border-color: #00d4ff;
  box-shadow: 0 0 0 3px rgba(0, 212, 255, 0.2);
  background: rgba(255, 255, 255, 0.15);
}

.form-group textarea {
  resize: vertical;
  min-height: 140px;
  font-family: inherit;
}

.quick-examples {
  margin-top: 1rem;
}

.example-buttons {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.example-btn {
  padding: 0.6rem 1rem;
  border: 1px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 10px;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.3s ease;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

.example-btn:hover {
  border-color: #00d4ff;
  background: rgba(0, 212, 255, 0.2);
  color: #00d4ff;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 212, 255, 0.2);
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  justify-content: center;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background: linear-gradient(135deg, #00d4ff 0%, #ff6b9d 100%);
  color: white;
  box-shadow: 0 8px 25px rgba(0, 212, 255, 0.4);
  border: none;
  position: relative;
  overflow: hidden;
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-primary:hover::before {
  left: 100%;
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-3px);
  box-shadow: 0 15px 35px rgba(0, 212, 255, 0.5);
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
  border: 2px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

.btn-secondary:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.btn-upload {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
  font-size: 1.1rem;
  padding: 1.2rem 2rem;
  border-radius: 12px;
  font-weight: 600;
}

.btn-upload:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
}

.btn-download {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
  font-size: 1.1rem;
  padding: 1.2rem 2.5rem;
}

.btn-download:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4);
}

.button-group {
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
}

/* Style controls */
.style-controls {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.color-controls {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

/* Size control */
.size-control {
  padding: 1.5rem;
  background: #f9fafb;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
}

.size-input-wrapper {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.size-slider {
  width: 100%;
  height: 8px;
  border-radius: 4px;
  background: #e5e7eb;
  outline: none;
  -webkit-appearance: none;
  appearance: none;
}

.size-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #4f46e5;
  cursor: pointer;
  box-shadow: 0 2px 6px rgba(79, 70, 229, 0.3);
}

.size-slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #4f46e5;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 6px rgba(79, 70, 229, 0.3);
}

.size-display {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.size-value {
  font-weight: 600;
  font-size: 1.1rem;
  color: #4f46e5;
  background: rgba(79, 70, 229, 0.1);
  padding: 0.5rem 1rem;
  border-radius: 8px;
}

.size-presets {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.size-preset {
  padding: 0.5rem 1rem;
  border: 2px solid #e5e7eb;
  background: white;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.size-preset:hover {
  border-color: #4f46e5;
  background: rgba(79, 70, 229, 0.05);
}

.size-preset.active {
  border-color: #4f46e5;
  background: #4f46e5;
  color: white;
}

.color-picker-wrapper {
  position: relative;
}

.color-button {
  width: 60px;
  height: 40px;
  border: 3px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.color-button:hover {
  border-color: #4f46e5;
  transform: scale(1.05);
}

.color-picker-popover {
  position: absolute;
  top: 50px;
  left: 0;
  z-index: 1000;
}

.color-picker-cover {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
}

/* Upload area */
.upload-area {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.upload-buttons {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.logo-preview {
  position: relative;
  display: inline-block;
  margin-top: 1rem;
}

.logo-preview img {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 8px;
  border: 2px solid #e5e7eb;
}

.remove-logo {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #ef4444;
  color: white;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  line-height: 1;
}

/* Logo controls */
.logo-controls {
  margin-top: 2rem;
  padding: 1.5rem;
  background: #f9fafb;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.logo-debug {
  background: linear-gradient(135deg, #dbeafe 0%, #e0f2fe 100%);
  border: 2px solid #3b82f6;
  border-radius: 12px;
  padding: 1.2rem;
  font-size: 0.9rem;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.logo-debug p {
  margin: 0.4rem 0;
  color: #1e40af;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo-debug strong {
  color: #1e3a8a;
}

.form-help {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.6);
  margin: 0.25rem 0 1.2rem 0;
  font-weight: 400;
}

.position-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.75rem;
  max-width: 240px;
  margin: 0 auto;
}

.position-btn {
  width: 70px;
  height: 70px;
  border: 2px solid #e5e7eb;
  background: white;
  border-radius: 12px;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.position-btn:hover {
  border-color: #4f46e5;
  background: rgba(79, 70, 229, 0.05);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.15);
}

.position-btn.active {
  border-color: #4f46e5;
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  color: white;
  box-shadow: 0 6px 20px rgba(79, 70, 229, 0.4);
  transform: translateY(-2px);
}

.position-icon {
  font-size: 1.5rem;
  margin-bottom: 0.25rem;
}

.position-text {
  font-size: 0.7rem;
  font-weight: 600;
  opacity: 0.8;
}

.position-btn.active .position-text {
  opacity: 1;
}

.logo-size-control {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  background: white;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.logo-size-slider {
  width: 100%;
  height: 8px;
  border-radius: 4px;
  background: linear-gradient(to right, #e5e7eb 0%, #10b981 100%);
  outline: none;
  -webkit-appearance: none;
  appearance: none;
}

.logo-size-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 22px;
  height: 22px;
  border-radius: 50%;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
  border: 2px solid white;
}

.logo-size-slider::-moz-range-thumb {
  width: 22px;
  height: 22px;
  border-radius: 50%;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
}

.logo-size-display {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.logo-size-value {
  font-weight: 700;
  font-size: 1.1rem;
  color: #10b981;
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(16, 185, 129, 0.05) 100%);
  padding: 0.6rem 1rem;
  border-radius: 8px;
  border: 1px solid rgba(16, 185, 129, 0.2);
}

.logo-size-presets {
  display: flex;
  gap: 0.5rem;
}

.logo-size-preset {
  padding: 0.5rem 1rem;
  border: 2px solid #e5e7eb;
  background: white;
  border-radius: 8px;
  font-size: 0.85rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.logo-size-preset:hover {
  border-color: #10b981;
  background: rgba(16, 185, 129, 0.05);
  transform: translateY(-1px);
}

.logo-size-preset.active {
  border-color: #10b981;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

/* Download section */
.download-section {
  text-align: center;
  margin-bottom: 2rem;
}

.download-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.btn-png {
  background: linear-gradient(135deg, #ff9500 0%, #ff6b00 100%);
  color: white;
  box-shadow: 0 8px 25px rgba(255, 149, 0, 0.4);
  position: relative;
  overflow: hidden;
}

.btn-png::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-png:hover::before {
  left: 100%;
}

.btn-png:hover:not(:disabled) {
  transform: translateY(-3px);
  box-shadow: 0 15px 35px rgba(255, 149, 0, 0.5);
}

.btn-pdf {
  background: linear-gradient(135deg, #e91e63 0%, #ad1457 100%);
  color: white;
  box-shadow: 0 8px 25px rgba(233, 30, 99, 0.4);
  position: relative;
  overflow: hidden;
}

.btn-pdf::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-pdf:hover::before {
  left: 100%;
}

.btn-pdf:hover:not(:disabled) {
  transform: translateY(-3px);
  box-shadow: 0 15px 35px rgba(233, 30, 99, 0.5);
}

.spinner {
  display: inline-block;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.format-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 2rem;
}

.format-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  padding: 2rem;
  text-align: left;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.format-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  border-radius: 16px;
  z-index: -1;
}

.format-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
}

.format-card h4 {
  margin: 0 0 1.2rem 0;
  background: linear-gradient(135deg, #00d4ff 0%, #ff6b9d 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-size: 1.2rem;
  font-weight: 700;
}

.format-card ul {
  margin: 0;
  padding-left: 1.2rem;
  color: rgba(255, 255, 255, 0.8);
}

.format-card li {
  margin: 0.6rem 0;
  font-size: 0.95rem;
  font-weight: 500;
}

.qr-info {
  margin-top: 2rem;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.qr-info::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  border-radius: 16px;
  z-index: -1;
}

.qr-info p {
  margin: 0.8rem 0;
  color: rgba(255, 255, 255, 0.9);
  font-size: 1rem;
  font-weight: 500;
  position: relative;
  z-index: 1;
}

.qr-info strong {
  color: #00d4ff;
  font-weight: 700;
}

/* Preview panel */
.preview-panel {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 3rem;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: sticky;
  top: 2rem;
  overflow: hidden;
}

.preview-panel::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  border-radius: 24px;
  z-index: -1;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.preview-container h3 {
  font-size: 1.8rem;
  font-weight: 800;
  background: linear-gradient(135deg, #00d4ff 0%, #ff6b9d 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
}

.refresh-btn {
  width: 45px;
  height: 45px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.3rem;
  transition: all 0.4s ease;
  color: rgba(255, 255, 255, 0.8);
}

.refresh-btn:hover {
  border-color: #00d4ff;
  background: rgba(0, 212, 255, 0.2);
  transform: rotate(180deg) scale(1.1);
  box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
}

.qr-preview {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 420px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  border: 2px dashed rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  padding: 2rem;
  position: relative;
  overflow: hidden;
}

.qr-preview::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 30% 30%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 70% 70%, rgba(255, 107, 157, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.qr-display {
  display: flex;
  justify-content: center;
  align-items: center;
}

.qr-image {
  border-radius: 12px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  background: white;
  padding: 1rem;
  transition: all 0.3s ease;
}

.qr-canvas {
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  transition: all 0.4s ease;
  border-radius: 16px;
  position: relative;
  z-index: 1;
}

.qr-canvas:hover {
  transform: scale(1.02);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4);
}

.qr-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.5rem;
  color: rgba(255, 255, 255, 0.6);
  text-align: center;
  position: relative;
  z-index: 1;
}

.qr-placeholder svg {
  opacity: 0.4;
  filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.1));
}

.qr-placeholder p {
  font-size: 1.1rem;
  font-weight: 500;
}

/* Responsive design */
@media (max-width: 1024px) {
  .content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .preview-panel {
    position: static;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 1rem;
  }

  .header {
    padding: 1.5rem 0;
  }

  .header h1 {
    font-size: 2rem;
  }

  .main {
    padding: 2rem 0;
  }

  .step-progress {
    gap: 1rem;
  }

  .step {
    min-width: 100px;
    padding: 0.8rem;
  }

  .step-icon {
    width: 2.5rem;
    height: 2.5rem;
  }

  .form-panel,
  .preview-panel {
    padding: 1.5rem;
  }

  .color-controls {
    grid-template-columns: 1fr;
  }

  .size-display {
    flex-direction: column;
    align-items: stretch;
  }

  .size-presets {
    justify-content: center;
  }

  .logo-size-display {
    flex-direction: column;
    align-items: stretch;
  }

  .logo-size-presets {
    justify-content: center;
  }

  .position-grid {
    max-width: 100%;
    justify-self: center;
  }

  .button-group {
    flex-direction: column;
  }

  .download-buttons {
    flex-direction: column;
    align-items: center;
  }

  .format-info {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .step-progress {
    flex-direction: column;
    align-items: center;
  }

  .step {
    flex-direction: row;
    min-width: 200px;
    justify-content: flex-start;
  }

  .step-icon {
    width: 2rem;
    height: 2rem;
  }
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.step-content {
  animation: fadeIn 0.5s ease-out;
}

/* Focus styles for accessibility */
.btn:focus,
.color-button:focus,
.step:focus {
  outline: 2px solid #4f46e5;
  outline-offset: 2px;
}

/* Loading states */
.btn:disabled {
  position: relative;
  overflow: hidden;
}

.btn:disabled::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}
